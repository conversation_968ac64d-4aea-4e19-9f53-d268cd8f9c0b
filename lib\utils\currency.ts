"use client"

import {
  Currency,
  CurrencyInfo,
  CurrencyDisplay,
  ExchangeRate,
  CurrencyConversion,
  PricingCalculationRequest,
  PricingCalculationResponse,
  CurrencyValidation,
  ExchangeRateValidation,
  LegacyCurrency,
  LegacyCurrencyInfo
} from "@/lib/types"

// =====================================================
// CURRENCY FORMATTING AND DISPLAY
// =====================================================

/**
 * Format currency amount with proper symbol and locale
 */
export function formatCurrency(
  amount: number, 
  currency: Currency | CurrencyDisplay,
  options?: {
    showSymbol?: boolean
    decimalPlaces?: number
    locale?: string
  }
): string {
  const { showSymbol = true, decimalPlaces, locale = 'en-US' } = options || {}
  
  let currencyInfo: CurrencyDisplay
  
  if (typeof currency === 'string') {
    // If currency is just a code, we need to get the info
    // This will be replaced with database lookup in production
    currencyInfo = getCurrencyDisplayInfo(currency)
  } else {
    currencyInfo = currency
  }
  
  const decimals = decimalPlaces ?? 2
  const formattedAmount = amount.toLocaleString(locale, {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  })
  
  if (!showSymbol) {
    return formattedAmount
  }
  
  const symbol = currencyInfo.symbol || currencyInfo.code
  
  // Handle RTL currencies
  if (currencyInfo.isRTL) {
    return `${formattedAmount} ${symbol}`
  } else {
    return `${symbol}${formattedAmount}`
  }
}

/**
 * Get currency display information
 * TODO: Replace with database lookup in production
 */
export function getCurrencyDisplayInfo(currencyCode: Currency): CurrencyDisplay {
  // Temporary hardcoded mapping - will be replaced with database lookup
  const currencyMap: Record<string, CurrencyDisplay> = {
    'USD': { code: 'USD', name: 'US Dollar', symbol: '$', arabicName: 'الدولار الأمريكي', isRTL: false },
    'SDG': { code: 'SDG', name: 'Sudanese Pound', symbol: 'ج.س.', arabicName: 'الجنيه السوداني', isRTL: true },
    'EGP': { code: 'EGP', name: 'Egyptian Pound', symbol: 'ج.م.', arabicName: 'الجنيه المصري', isRTL: true },
    'SAR': { code: 'SAR', name: 'Saudi Riyal', symbol: 'ر.س', arabicName: 'الريال السعودي', isRTL: true },
    'AED': { code: 'AED', name: 'UAE Dirham', symbol: 'د.إ', arabicName: 'الدرهم الإماراتي', isRTL: true },
    'EUR': { code: 'EUR', name: 'Euro', symbol: '€', arabicName: 'اليورو', isRTL: false },
    'GBP': { code: 'GBP', name: 'British Pound', symbol: '£', arabicName: 'الجنيه الإسترليني', isRTL: false }
  }
  
  return currencyMap[currencyCode] || {
    code: currencyCode,
    name: currencyCode,
    symbol: currencyCode,
    isRTL: false
  }
}

/**
 * Parse currency amount from formatted string
 */
export function parseCurrencyAmount(formattedAmount: string, currency: Currency): number {
  const currencyInfo = getCurrencyDisplayInfo(currency)
  
  // Remove currency symbol and spaces
  let cleanAmount = formattedAmount
    .replace(currencyInfo.symbol, '')
    .replace(/\s/g, '')
    .replace(/,/g, '')
  
  const amount = parseFloat(cleanAmount)
  return isNaN(amount) ? 0 : amount
}

// =====================================================
// CURRENCY CONVERSION UTILITIES
// =====================================================

/**
 * Convert amount between currencies using exchange rate
 */
export function convertCurrencyAmount(
  amount: number,
  fromCurrency: Currency,
  toCurrency: Currency,
  exchangeRate: number
): number {
  if (fromCurrency === toCurrency) {
    return amount
  }
  
  return amount * exchangeRate
}

/**
 * Calculate conversion with fees
 */
export function calculateConversionWithFees(
  amount: number,
  exchangeRate: number,
  feeRate: number = 0
): { convertedAmount: number; fee: number; totalReceived: number } {
  const convertedAmount = amount * exchangeRate
  const fee = convertedAmount * feeRate
  const totalReceived = convertedAmount - fee
  
  return {
    convertedAmount,
    fee,
    totalReceived
  }
}

/**
 * Get cross-currency exchange rate via USD
 */
export function calculateCrossRate(
  fromCurrencyToUSD: number,
  toCurrencyToUSD: number
): number {
  if (fromCurrencyToUSD === 0) {
    throw new Error('Invalid exchange rate: fromCurrencyToUSD cannot be zero')
  }
  
  return toCurrencyToUSD / fromCurrencyToUSD
}

// =====================================================
// VALIDATION UTILITIES
// =====================================================

/**
 * Validate currency code format
 */
export function validateCurrencyCode(code: string): CurrencyValidation {
  const errors: string[] = []
  
  if (!code) {
    errors.push('Currency code is required')
  } else if (code.length !== 3) {
    errors.push('Currency code must be exactly 3 characters')
  } else if (!/^[A-Z]{3}$/.test(code)) {
    errors.push('Currency code must be 3 uppercase letters')
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * Validate exchange rate
 */
export function validateExchangeRate(rate: number, fromCurrency: Currency, toCurrency: Currency): ExchangeRateValidation {
  const errors: string[] = []
  const warnings: string[] = []
  
  if (rate <= 0) {
    errors.push('Exchange rate must be positive')
  }
  
  if (fromCurrency === toCurrency && rate !== 1) {
    errors.push('Exchange rate for same currency must be 1.0')
  }
  
  // Warn about unusual rates
  if (rate > 10000) {
    warnings.push('Exchange rate seems unusually high')
  } else if (rate < 0.0001) {
    warnings.push('Exchange rate seems unusually low')
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * Validate conversion amount
 */
export function validateConversionAmount(
  amount: number,
  currency: Currency,
  minimumAmounts?: Record<Currency, number>
): CurrencyValidation {
  const errors: string[] = []
  
  if (amount <= 0) {
    errors.push('Amount must be positive')
  }
  
  const minimumAmount = minimumAmounts?.[currency] || 0
  if (amount < minimumAmount) {
    errors.push(`Minimum conversion amount for ${currency} is ${minimumAmount}`)
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}



/**
 * Get default currency based on environment
 */
export function getDefaultCurrency(): Currency {
  // USD is now the default currency for all new users
  return 'USD'
}

/**
 * Get enabled currencies for current client
 */
export function getEnabledCurrencies(): Currency[] {
  // In production, this would come from client configuration
  // USD is always first as the default currency
  return ['USD', 'SDG', 'EGP']
}

// =====================================================
// UTILITY CONSTANTS
// =====================================================

export const CURRENCY_DECIMAL_PLACES: Record<string, number> = {
  'USD': 2,
  'EUR': 2,
  'GBP': 2,
  'SDG': 2,
  'EGP': 2,
  'SAR': 2,
  'AED': 2,
  'BTC': 8,
  'ETH': 6
}

export const RTL_CURRENCIES = ['SDG', 'EGP', 'SAR', 'AED', 'IQD', 'JOD', 'KWD', 'LBP', 'LYD', 'MAD', 'OMR', 'QAR', 'SYP', 'TND', 'YER']

export const MAJOR_CURRENCIES = ['USD', 'EUR', 'GBP', 'JPY', 'CHF', 'CAD', 'AUD']

export const MIDDLE_EAST_CURRENCIES = ['SDG', 'EGP', 'SAR', 'AED', 'IQD', 'JOD', 'KWD', 'LBP', 'OMR', 'QAR']
