"use client"

import React, { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs"
import {
  Plus,
  Trash2,
  Upload,
  Save,
  X,
  Package,
  Settings,
  Eye,
  Image as ImageIcon,
  Shield,
  Check
} from "lucide-react"
import { ProductTemplate, ProductFormState, DynamicField, ProductPackage, ProductCategory } from "@/lib/types"
import { createProduct, updateProduct } from "@/lib/services/productService"
import { getCategories } from "@/lib/services/categoryService"
import { PackageEditor } from "./PackageEditor"
import { FieldEditor } from "./FieldEditor"

interface ProductFormProps {
  product?: ProductTemplate
  onSave: (product: ProductTemplate) => void
  onCancel: () => void
  isEditing?: boolean
}

export function ProductForm({ product, onSave, onCancel, isEditing = false }: ProductFormProps) {
  // ## TODO: Add form validation with react-hook-form
  // ## TODO: Implement image upload to Supabase Storage
  
  const [formData, setFormData] = useState<ProductFormState>({
    name: "",
    description: "",
    category: "",
    basePrice: 0,
    estimatedTime: "فوري",
    fields: [],
    packages: [],
    features: [],
    tags: [],
    isActive: true,
    isFeatured: false
  })
  
  const [isLoading, setIsLoading] = useState(false)
  const [activeTab, setActiveTab] = useState("basic")
  const [categories, setCategories] = useState<ProductCategory[]>([])
  const [newFeature, setNewFeature] = useState("")
  const [newTag, setNewTag] = useState("")

  // Load categories on mount
  useEffect(() => {
    try {
      const loadedCategories = getCategories()
      setCategories(loadedCategories)
    } catch (error) {
      console.error("Error loading categories:", error)
    }
  }, [])

  // Initialize form data when editing
  useEffect(() => {
    if (product && isEditing) {
      setFormData({
        name: product.name,
        description: product.description || "",
        category: product.category,
        basePrice: product.basePrice || 0,
        estimatedTime: product.estimatedTime || "فوري",
        fields: product.fields,
        packages: product.packages,
        features: product.features || [],
        tags: product.tags || [],
        isActive: product.isActive,
        isFeatured: product.isFeatured || false
      })
    }
  }, [product, isEditing])

  /**
   * Handle form submission
   */
  const handleSubmit = async () => {
    // ## TODO: Add comprehensive form validation
    if (!formData.name.trim()) {
      alert("يرجى إدخال اسم المنتج")
      return
    }
    
    if (!formData.category.trim()) {
      alert("يرجى إدخال فئة المنتج")
      return
    }

    if (formData.packages.length === 0) {
      alert("يرجى إضافة حزمة واحدة على الأقل")
      return
    }

    try {
      setIsLoading(true)
      
      const productData: Omit<ProductTemplate, 'id' | 'createdAt' | 'updatedAt'> = {
        name: formData.name,
        description: formData.description || undefined,
        category: formData.category,
        basePrice: formData.basePrice || undefined,
        estimatedTime: formData.estimatedTime,
        // Set default values for gaming products
        productType: "digital",
        processingType: "instant",
        deliveryType: "code_based", // Default for gaming products
        fields: formData.fields,
        packages: formData.packages,
        features: formData.features,
        tags: formData.tags,
        isActive: formData.isActive,
        isFeatured: formData.isFeatured,
        createdBy: undefined // ## TODO: Get from Supabase Auth
      }

      let savedProduct: ProductTemplate
      
      if (isEditing && product) {
        savedProduct = await updateProduct(product.id, productData)
      } else {
        savedProduct = await createProduct(productData)
      }
      
      onSave(savedProduct)
    } catch (error) {
      console.error("Error saving product:", error)
      alert("حدث خطأ أثناء حفظ المنتج")
    } finally {
      setIsLoading(false)
    }
  }

  /**
   * Add feature to list
   */
  const addFeature = () => {
    if (newFeature.trim()) {
      setFormData(prev => ({
        ...prev,
        features: [...prev.features, newFeature.trim()]
      }))
      setNewFeature("")
    }
  }

  /**
   * Remove feature from list
   */
  const removeFeature = (index: number) => {
    setFormData(prev => ({
      ...prev,
      features: prev.features.filter((_, i) => i !== index)
    }))
  }

  /**
   * Add tag to list
   */
  const addTag = () => {
    if (newTag.trim()) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }))
      setNewTag("")
    }
  }

  /**
   * Remove tag from list
   */
  const removeTag = (index: number) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter((_, i) => i !== index)
    }))
  }

  /**
   * Handle packages update
   */
  const handlePackagesUpdate = (packages: ProductPackage[]) => {
    setFormData(prev => ({ ...prev, packages }))
  }

  /**
   * Handle fields update
   */
  const handleFieldsUpdate = (fields: DynamicField[]) => {
    setFormData(prev => ({ ...prev, fields }))
  }

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4 bg-slate-700">
          <TabsTrigger value="basic" className="text-white">المعلومات الأساسية</TabsTrigger>
          <TabsTrigger value="packages" className="text-white">الحزم والأسعار</TabsTrigger>
          <TabsTrigger value="fields" className="text-white">الحقول المخصصة</TabsTrigger>
          <TabsTrigger value="settings" className="text-white">الإعدادات</TabsTrigger>
        </TabsList>

        {/* Basic Information Tab */}
        <TabsContent value="basic" className="space-y-4">
          <Card className="bg-slate-700/50 border-slate-600">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Package className="h-5 w-5" />
                المعلومات الأساسية
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Product Name */}
              <div>
                <Label className="text-slate-300">اسم المنتج *</Label>
                <Input
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="مثال: شحن يوسي PUBG Mobile"
                  className="bg-slate-600 border-slate-500 text-white"
                />
              </div>

              {/* Description */}
              <div>
                <Label className="text-slate-300">الوصف</Label>
                <Textarea
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="وصف المنتج..."
                  className="bg-slate-600 border-slate-500 text-white min-h-[100px]"
                />
              </div>

              {/* Category and Type */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label className="text-slate-300">الفئة *</Label>
                  <Select
                    value={formData.category}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}
                  >
                    <SelectTrigger className="bg-slate-600 border-slate-500 text-white">
                      <SelectValue placeholder="اختر الفئة..." />
                    </SelectTrigger>
                    <SelectContent className="bg-slate-600 border-slate-500">
                      {categories.map((category) => (
                        <SelectItem key={category.id} value={category.name} className="text-white hover:bg-slate-700">
                          <div className="flex items-center gap-2">
                            <div className="w-6 h-6 flex items-center justify-center overflow-hidden rounded">
                              <img
                                src={category.image}
                                alt={category.name}
                                className="w-full h-full object-cover"
                              />
                            </div>
                            <span>{category.name}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {categories.length === 0 && (
                    <p className="text-slate-400 text-xs mt-1">
                      لا توجد فئات. يرجى إضافة فئات من تبويب "الفئات" أولاً.
                    </p>
                  )}
                </div>

              </div>

              {/* Image Upload */}
              <div>
                <Label className="text-slate-300">صورة المنتج</Label>
                <div className="border-2 border-dashed border-slate-600 rounded-lg p-6 text-center">
                  <ImageIcon className="h-12 w-12 text-slate-500 mx-auto mb-2" />
                  <p className="text-slate-400 mb-2">اسحب الصورة هنا أو انقر للتحديد</p>
                  <Button variant="outline" className="border-slate-600 text-slate-300">
                    <Upload className="h-4 w-4 mr-2" />
                    تحديد صورة
                  </Button>
                  <p className="text-xs text-slate-500 mt-2">
                    ## TODO: تنفيذ رفع الصور إلى Supabase Storage
                  </p>
                </div>
              </div>


            </CardContent>
          </Card>
        </TabsContent>

        {/* Packages Tab */}
        <TabsContent value="packages">
          <PackageEditor
            packages={formData.packages}
            onPackagesUpdate={handlePackagesUpdate}
          />
        </TabsContent>

        {/* Fields Tab */}
        <TabsContent value="fields">
          <FieldEditor
            fields={formData.fields}
            onFieldsUpdate={handleFieldsUpdate}
          />
        </TabsContent>

        {/* Settings Tab */}
        <TabsContent value="settings" className="space-y-4">
          <Card className="bg-slate-700/50 border-slate-600">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Settings className="h-5 w-5" />
                إعدادات المنتج
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Status Switches */}
              <div className="flex items-center justify-between">
                <Label className="text-slate-300">المنتج نشط</Label>
                <Switch
                  checked={formData.isActive}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isActive: checked }))}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <Label className="text-slate-300">منتج مميز</Label>
                <Switch
                  checked={formData.isFeatured}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isFeatured: checked }))}
                />
              </div>

              {/* Product Features Manager */}
              <div className="space-y-4 border-t border-slate-600 pt-4">
                <div className="flex items-center gap-2">
                  <Shield className="h-5 w-5 text-green-400" />
                  <h4 className="text-white font-medium">مميزات المنتج</h4>
                </div>
                <p className="text-slate-400 text-sm">
                  هذه المميزات ستظهر في صفحة المنتج في المتجر لإقناع العملاء بالشراء
                </p>

                <div className="space-y-3">
                  {/* Add new feature */}
                  <div className="flex gap-2">
                    <Input
                      value={newFeature}
                      onChange={(e) => setNewFeature(e.target.value)}
                      placeholder="مثال: 🚀 تسليم فوري للأكواد"
                      className="bg-slate-600 border-slate-500 text-white"
                      onKeyPress={(e) => e.key === 'Enter' && addFeature()}
                    />
                    <Button onClick={addFeature} size="sm" className="bg-green-600 hover:bg-green-700">
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>

                  {/* Current features */}
                  <div className="space-y-2">
                    {formData.features.map((feature, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-slate-700/50 rounded-lg border border-slate-600">
                        <div className="flex items-center gap-3">
                          <Check className="h-4 w-4 text-green-400" />
                          <span className="text-slate-300">{feature}</span>
                        </div>
                        <Button
                          onClick={() => removeFeature(index)}
                          size="sm"
                          variant="ghost"
                          className="text-red-400 hover:text-red-300 hover:bg-red-500/10"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>

                  {/* Default features suggestions */}
                  {formData.features.length === 0 && (
                    <div className="bg-blue-500/10 border border-blue-500/30 rounded-lg p-4">
                      <h5 className="text-blue-300 font-medium mb-2">اقتراحات مميزات شائعة:</h5>
                      <div className="grid grid-cols-1 gap-2">
                        {[
                          "🚀 تسليم فوري للأكواد",
                          "💯 ضمان الجودة والأمان",
                          "🔒 معاملات آمنة ومشفرة",
                          "📱 يعمل على جميع الأجهزة",
                          "🎮 دعم فني متخصص",
                          "💳 طرق دفع متعددة"
                        ].map((suggestion, idx) => (
                          <Button
                            key={idx}
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setNewFeature(suggestion)
                              addFeature()
                            }}
                            className="justify-start text-blue-300 hover:bg-blue-500/20 h-auto py-2"
                          >
                            {suggestion}
                          </Button>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Tags */}
              <div>
                <Label className="text-slate-300">علامات البحث</Label>
                <div className="space-y-2">
                  <div className="flex gap-2">
                    <Input
                      value={newTag}
                      onChange={(e) => setNewTag(e.target.value)}
                      placeholder="أضف علامة جديدة..."
                      className="bg-slate-600 border-slate-500 text-white"
                      onKeyPress={(e) => e.key === 'Enter' && addTag()}
                    />
                    <Button onClick={addTag} size="sm" className="bg-blue-600 hover:bg-blue-700">
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {formData.tags.map((tag, index) => (
                      <Badge key={index} variant="outline" className="border-slate-500 text-slate-300">
                        {tag}
                        <button
                          onClick={() => removeTag(index)}
                          className="ml-2 text-slate-400 hover:text-white"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Action Buttons */}
      <div className="flex justify-end gap-3 pt-4 border-t border-slate-600">
        <Button
          variant="outline"
          onClick={onCancel}
          className="border-slate-600 text-slate-300 hover:bg-slate-700"
        >
          إلغاء
        </Button>
        <Button
          onClick={handleSubmit}
          disabled={isLoading}
          className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700"
        >
          <Save className="h-4 w-4 mr-2" />
          {isLoading ? "جاري الحفظ..." : isEditing ? "تحديث المنتج" : "إنشاء المنتج"}
        </Button>
      </div>
    </div>
  )
}
